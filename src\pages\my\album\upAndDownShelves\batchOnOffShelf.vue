<template>
	<view class="out">
		<loading-common ref="loadingCommonRef"></loading-common>
		<!-- 查看图片列表 -->
		<tt-swiper-lookimagelist ref="lookimagelistRef"></tt-swiper-lookimagelist>
		<tt-toast ref="ttToastRef"></tt-toast>

		<u-loading-page style="z-index: 99999" color="#999999" loadingColor="#999999" iconSize="40" fontSize="24" :loading="!showPage"></u-loading-page>
		<top-nav :zIndex="10" bgColor="#ffffff" :class="showPage ? '' : 'hiddenStyle'">
			<template v-slot:content>
				<view class="navTop">
					<view class="navTop-left">
						<image id="clickOpacity" @click="getNavigateBack()" class="navTop-left-image" src="@/static/image/common/left_return.png" mode="heightFix"></image>
					</view>
					<view class="navTop-center">
						<view class="navTop-center-title">批量上下架</view>
					</view>
				</view>
			</template>
		</top-nav>
		<template v-if="showPage">
			<view class="tabs">
				<template v-for="(item, index) in tabList">
					<view class="tabs-item" @click="onclickTab(index)">
						<view class="tabs-item-c">
							<text :class="['tabs-item-c-text', current == index ? 'checked' : '']">{{ item.name }}</text>
							<view class="tabs-item-c-divider"></view>
						</view>
						<image src="../../../static/image/common/updown_tab.png" class="tabs-item-underline" v-show="current == index"></image>
						<!-- <view :class="['tabs-item-underline', current == index ? 'checkedUnderline' : '']"></view> -->
					</view>
				</template>
			</view>

			<swiper :current="current" @change="swiperChange" style="height: calc(100vh - 168rpx - 88rpx)">
				<swiper-item class="swiperitem">
					<view class="boxContent">
						<tt-refresher @onReachBottomScroll="onReachBottomScroll" @onRefresherrefresh="onRefresherrefresh">
							<template v-slot:content>
								<template v-if="dynamicList.length == 0 && !tabList[current].pages.pageTurn">
									<view class="not_content">
										<image class="not_content-image" src="@/static/image/common/not_content_trend.png" mode="scaleToFill"></image>
										<text>暂无动态内容</text>
									</view>
								</template>

								<template v-for="(item, index) in dynamicList" >
									<view id="clickBg" class="dline" :key="item.id">
										<view class="dline-left" @click="getDelIdlist(item)">
											<template v-if="delIdlist.indexOf(item.id) == -1">
												<image class="dline-left-change" src="@/static/image/common/check_n.png" mode="scaleToFill"></image>
											</template>
											<template v-else>
												<image class="dline-left-change" src="@/static/image/common/check_p.png" mode="scaleToFill"></image>
											</template>
										</view>
										<view class="dline-right">
											<view :class="['dline-right-top', item.dynamic_img != '' ? '' : 'topBg']">{{ item.dynamic_title }}</view>
											<view class="dline-right-bottom" v-if="item.dynamic_img != ''">
												<template v-for="(imgitem, imgindex) in getRightsamllImage(item)">
													<template v-if="imgindex < 5">
														<view class="imageItem" @click="openlookimagelist(item, imgindex)">
															<image class="imageItem-image" :src="getImagesWeservImg(imgitem)" mode="aspectFill"></image>
															<template v-if="imgindex == 4">
																<view class="imageItem-mask">
																	<text>+{{ getRightsamllImage(item).length - 4 }}</text>
																</view>
															</template>
														</view>
													</template>
												</template>
											</view>
										</view>
									</view>
								</template>

								<template v-if="dynamicList.length > 0 && isLoading && tabList[current].pages.pageTurn">
									<view class="notmorelist">
										<text>正在加载...</text>
									</view>
								</template>
								<template v-if="dynamicList.length > 0 && !tabList[current].pages.pageTurn">
									<view class="notmorelist">
										<text>到底了~</text>
									</view>
								</template>
							</template>
						</tt-refresher>
					</view>
				</swiper-item>
				<swiper-item class="swiperitem">
					<view class="boxContent">
						<tt-refresher @onReachBottomScroll="onReachBottomScroll" @onRefresherrefresh="onRefresherrefresh">
							<template v-slot:content>
								<template v-if="dynamicList2.length == 0 && !tabList[current].pages.pageTurn">
									<view class="not_content">
										<image class="not_content-image" src="@/static/image/common/not_content_trend.png" mode="scaleToFill"></image>
										<text>暂无动态内容</text>
									</view>
								</template>

								<template v-for="(item, index) in dynamicList2" >
									<view id="clickBg" class="dline" :key="item.id">
										<view class="dline-left" @click="getDelIdlist(item)">
											<template v-if="delIdlist2.indexOf(item.id) == -1">
												<image class="dline-left-change" src="@/static/image/common/check_n.png" mode="scaleToFill"></image>
											</template>
											<template v-else>
												<image class="dline-left-change" src="@/static/image/common/check_p.png" mode="scaleToFill"></image>
											</template>
										</view>
										<view class="dline-right">
											<view :class="['dline-right-top', item.dynamic_img != '' ? '' : 'topBg']">{{ item.dynamic_title }}</view>
											<view class="dline-right-bottom" v-if="item.dynamic_img != ''">
												<template v-for="(imgitem, imgindex) in getRightsamllImage(item)">
													<template v-if="imgindex < 5">
														<view class="imageItem" @click="openlookimagelist(item, imgindex)">
															<image class="imageItem-image" :src="getImagesWeservImg(imgitem)" mode="aspectFill"></image>
															<template v-if="imgindex == 4">
																<view class="imageItem-mask">
																	<text>+{{ getRightsamllImage(item).length - 4 }}</text>
																</view>
															</template>
														</view>
													</template>
												</template>
											</view>
										</view>
									</view>
								</template>

								<template v-if="dynamicList2.length > 0 && isLoading && tabList[current].pages.pageTurn">
									<view class="notmorelist">
										<text>正在加载...</text>
									</view>
								</template>
								<template v-if="dynamicList2.length > 0 && !tabList[current].pages.pageTurn">
									<view class="notmorelist">
										<text>到底了~</text>
									</view>
								</template>
							</template>
						</tt-refresher>
					</view>
				</swiper-item>
			</swiper>

			<!-- 占位 -->
			<view class="footer_content_z"></view>
			<template v-if="current == 0">
				<view class="footerBtnBox" :style="{ paddingBottom: paddingBottom }">
					<view class="footerBtnBox-change">
						<template v-if="!allchange">
							<image id="clickOpacity" class="footerBtnBox-change-image" @click="getAllChange()" src="@/static/image/common/check_n.png" mode="scaleToFill"></image>
						</template>
						<template v-else>
							<image id="clickOpacity" class="footerBtnBox-change-image" @click="getAllChange()" src="@/static/image/common/check_p.png" mode="scaleToFill"></image>
						</template>
						<view id="clickOpacity" class="footerBtnBox-change-c" @click="popShow()">
							<text class="footerBtnBox-change-c-text">选中{{ delIdlist.length }}条</text>
							<image src="../../../static/image/common/check_all_icon.png" class="footerBtnBox-change-c-img"></image>
						</view>
					</view>
					<template v-if="delIdlist.length > 0">
						<view id="clickBg" class="footerBtnBox-btn" @click="openOnOffPopupRef()">
							<text>下架</text>
						</view>
					</template>
					<template v-else>
						<view class="footerBtnBox-notbtn">
							<text>下架</text>
						</view>
					</template>
				</view>
			</template>

			<template v-else>
				<view class="footerBtnBox" :style="{ paddingBottom: paddingBottom }">
					<view class="footerBtnBox-change">
						<template v-if="!allchange2">
							<image id="clickOpacity" class="footerBtnBox-change-image" @click="getAllChange()" src="@/static/image/common/check_n.png" mode="scaleToFill"></image>
						</template>
						<template v-else>
							<image id="clickOpacity" class="footerBtnBox-change-image" @click="getAllChange()" src="@/static/image/common/check_p.png" mode="scaleToFill"></image>
						</template>
						<view id="clickOpacity" class="footerBtnBox-change-c" @click="popShow()">
							<text class="footerBtnBox-change-c-text">选中{{ delIdlist2.length }}条</text>
							<image src="../../../static/image/common/check_all_icon.png" class="footerBtnBox-change-c-img"></image>
						</view>
					</view>
					<template v-if="delIdlist2.length > 0">
						<!-- 			<view class="footerBtnBox-btn2" @click="openDelPopup()">
							<text>删除</text>
						</view> -->
						<view id="clickBg" class="footerBtnBox-btn" @click="openOnOffPopupRef()">
							<text>上架</text>
						</view>
					</template>
					<template v-else>
						<!-- 			<view class="footerBtnBox-notbtn2">
							<text>删除</text>
						</view> -->
						<view class="footerBtnBox-notbtn">
							<text>上架</text>
						</view>
					</template>
				</view>
			</template>
		</template>

		<popup-common
			title="温馨提示"
			:text="`删除后不可恢复，确定要删除<font color=#6CBE70>${delIdlist2.length}</font>件商品？`"
			btnTextClose="取消"
			btnText="确定"
			ref="popupCommonDelRef"
			@confirm="getCommonDel()"
		></popup-common>

		<popup-common
			title="温馨提示"
			:text="`确定要下架<font color=#6CBE70>${delIdlist.length}</font>件商品吗？`"
			btnTextClose="取消"
			btnText="确定"
			ref="popupCommonOffRef"
			@confirm="getDynamicBatchUpperDown()"
		></popup-common>
		<popup-common
			title="温馨提示"
			:text="`确定要上架<font color=#6CBE70>${delIdlist2.length}</font>件商品吗？`"
			btnTextClose="取消"
			btnText="确定"
			ref="popupCommonOnRef"
			@confirm="getDynamicBatchUpperDown()"
		></popup-common>

		<popup-common
			title="自定义选择数量"
			:show-input="false"
			:show-text="true"
			input-type="number"
			inputPlaceHolder="请输入自定义数量"
			btnTextClose="取消"
			btnText="确定"
			ref="popupCommonNumRef"
			@confirm="getCommonNum"
		></popup-common>

		<!-- 操作弹窗 -->
		<uni-popup style="z-index: 4010" :is-mask-click="isMaskClick" ref="setPopupRef" :safe-area="false" type="bottom">
			<view class="popup" :style="{ marginBottom: popMarginBottom }">
				<view class="popup-bottomBox">
					<view id="clickBg" class="popup-bottomBox-line" @click="openNumPopup()">
						<text>自定义数量</text>
					</view>
					<view class="popup-bottomBox-divider"></view>
					<view id="clickBg" class="popup-bottomBox-line" @click="getAllChange()">
						<text>全部</text>
					</view>
				</view>
			</view>
		</uni-popup>

		<pageSwipeBack :leftDrag="true"></pageSwipeBack>
	</view>
</template>

<script>
	import { getImagesWeserv } from '@/static/utils/uni-common.js';
import { dynamicBatchDel, getShopHomeDynamicList, dynamicBatchOperate } from '@/static/api/common/common.js';

export default {
	data() {
		return {
			showPage: false,
			tabList: [
				{
					type: 4,
					name: '已上架',
					pages: {
						page: 1,
						limit: 20,
						isLoad: false,
						pageTurn: true
					}
				},
				{
					type: 5,
					name: '已下架',
					pages: {
						page: 1,
						limit: 20,
						isLoad: false,
						pageTurn: true
					}
				}
			],
			isLoading: false,
			current: 0,
			delLoading: false,

			dynamicList: [],
			delIdlist: [],
			allchange: false,

			dynamicList2: [],
			delIdlist2: [],
			allchange2: false,
			isMaskClick: false,
			loginUserInfo: {},
			isIos: false,
			isAndroid: false,
			paddingBottom: '0rpx',
			popMarginBottom: '100rpx'
		};
	},

	onLoad() {
		// #ifdef H5
		let uaAll = window.navigator.userAgent;
		this.isAndroid = uaAll.indexOf(`android_${getApp().globalData.app_name}`) > -1;
		this.isIos = uaAll.indexOf(`ios_${getApp().globalData.app_name}`) > -1;
		if (this.isIos) {
			this.paddingBottom = '68rpx';
			this.popMarginBottom = '168rpx';
		} else if (this.isAndroid) {
			this.paddingBottom = '0rpx';
			this.popMarginBottom = '100rpx';
		}
		// #endif
		this.loginUserInfo = uni.getStorageSync('userInfo').userInfo;

		this.getDynamicList();
	},

	methods: {
		getImagesWeservImg(url){
			return getImagesWeserv(url)
		},
		openNumPopup() {
			this.popClose();
			this.$refs.popupCommonNumRef.open();
		},
		getCommonNum(value) {
			this.selectNum(value);
		},
		popClose() {
			this.$refs.setPopupRef.close();
		},
		popShow() {
			this.isMaskClick = false;
			this.$refs.setPopupRef.open();
			setTimeout(() => {
				this.isMaskClick = true;
			}, 200);
		},
		/* 导航返回按钮 */
		getNavigateBack() {
			uni.navigateBack({
				delta: 1
			});
		},
		swiperChange(event) {
			this.current = event.detail.current;
			let isLoad = true;
			if (this.current == 0) {
				if (!this.tabList[this.current].pages.isLoad) {
					this.tabList[this.current].pages = {
						page: 1,
						limit: 20,
						isLoad: false,
						pageTurn: true
					};

					this.delIdlist = [];
					isLoad = false;
				}
			} else {
				if (!this.tabList[this.current].pages.isLoad) {
					this.tabList[this.current].pages = {
						page: 1,
						limit: 20,
						isLoad: false,
						pageTurn: true
					};

					this.delIdlist2 = [];
					isLoad = false;
				}
			}
			if (!isLoad) {
				this.getDynamicList();
			}
		},
		onclickTab(index) {
			this.current = index;
		},
		// 查看动态图片
		openlookimagelist(item, index) {
			// let list = this.getRightsamllImage(item);
			this.$refs.lookimagelistRef.open(item, index, false, true);
		},
		// 触底
		onReachBottomScroll() {
			let that = this;
			that.getDynamicList();
		},
		// 刷新
		onRefresherrefresh() {
			let that = this;
			that.tabList[that.current].pages = {
				page: 1,
				limit: 20,
				isLoad: false,
				pageTurn: true
			};

			if (that.current == 0) {
				that.delIdlist = [];
			} else {
				that.delIdlist2 = [];
			}

			that.getDynamicList();
		},

		// 刷新
		refreshAll() {
			let that = this;
			that.tabList[0].pages = {
				page: 1,
				limit: 20,
				isLoad: false,
				pageTurn: true
			};

			that.tabList[1].pages = {
				page: 1,
				limit: 20,
				isLoad: false,
				pageTurn: true
			};

			that.delIdlist = [];
			that.delIdlist2 = [];

			that.getDynamicList();
		},
		getDynamicList() {
			let that = this;
			let user_id = this.loginUserInfo.user_id;
			let pages, dynamicList;

			pages = that.tabList[that.current].pages;
			if (that.current == 0) {
				dynamicList = that.dynamicList;
			} else {
				dynamicList = that.dynamicList2;
			}
			let data = {
				page: pages.page,
				limit: pages.limit,
				user_id: user_id,
				type: that.tabList[that.current].type,
				order_type: '1'
			};
			if (that.isLoading || !pages.pageTurn) return;
			that.isLoading = true;
			getShopHomeDynamicList(data).then((res) => {
				that.isLoading = false;
				if (res.code == 0) {
					that.tabList[that.current].pages.isLoad = true;
					if (pages.page == 1) {
						dynamicList = res.data;
					} else {
						if (res.data.length > 0) {
							dynamicList = dynamicList.concat(res.data);
						} else {
							pages.page = pages.page - 1;
						}
					}
					if (res.data.length < pages.limit) {
						pages.pageTurn = false;
					} else {
						pages.pageTurn = true;
						pages.page = pages.page + 1;
					}
				} else {
					that.$refs.ttToastRef.text = res.message;
					that.$refs.ttToastRef.open();
				}
				if (that.current == 0) {
					that.pages = pages;
					that.dynamicList = dynamicList;
				} else {
					that.pages2 = pages;
					that.dynamicList2 = dynamicList;
				}
				that.isNewAllChange();
				setTimeout(() => {
					that.showPage = true;
				}, 500);
			});
		},
		/* 获取动态首页列表 -- 右侧小图片 */
		getRightsamllImage(item) {
			let list = [];
			list = item.dynamic_img.split('|');
			// if (item.cover_img != '') {
			// 	list.splice(0, 0, item.cover_img);
			// }
			return list;
		},

		getDelIdlist(item) {
			let delIdlist = this.current == 0 ? this.delIdlist : this.delIdlist2;
			let dynamicList = this.current == 0 ? this.dynamicList : this.dynamicList2;
			if (delIdlist.indexOf(item.id) == -1) {
				delIdlist.push(item.id);
			} else {
				let i = delIdlist.indexOf(item.id);
				delIdlist.splice(i, 1);
			}
			if (this.current == 0) {
				this.delIdlist = delIdlist;
				if (delIdlist.length < dynamicList.length) {
					this.allchange = false;
				} else {
					this.allchange = true;
				}
			} else {
				this.delIdlist2 = delIdlist;
				if (delIdlist.length < dynamicList.length) {
					this.allchange2 = false;
				} else {
					this.allchange2 = true;
				}
			}
		},
		selectNum(num) {
			let that = this;
			if (this.current == 0) {
				that.delIdlist = [];
			} else {
				that.delIdlist2 = [];
			}
			let delIdlist = [];
			let dynamicList = this.current == 0 ? that.dynamicList : that.dynamicList2;
			if (num > dynamicList.length) {
				num = dynamicList.length;
			}
			for (let i = 0; i < num; i++) {
				delIdlist.push(dynamicList[i].id);
			}

			if (this.current == 0) {
				that.delIdlist = delIdlist;
				if (delIdlist.length < dynamicList.length) {
					that.allchange = false;
				} else {
					that.allchange = true;
				}
			} else {
				that.delIdlist2 = delIdlist;
				if (delIdlist.length < dynamicList.length) {
					that.allchange2 = false;
				} else {
					that.allchange2 = true;
				}
			}
		},
		getAllChange() {
			this.popClose();
			let that = this;
			let delIdlist = this.current == 0 ? this.delIdlist : this.delIdlist2;
			let dynamicList = this.current == 0 ? that.dynamicList : that.dynamicList2;
			if (delIdlist.length < dynamicList.length) {
				for (let i = 0; i < dynamicList.length; i++) {
					if (delIdlist.indexOf(dynamicList[i].id) == -1) {
						delIdlist.push(dynamicList[i].id);
					}
				}
				if (this.current == 0) {
					that.delIdlist = delIdlist;
					that.allchange = true;
				} else {
					that.delIdlist2 = delIdlist;
					that.allchange2 = true;
				}
			} else {
				if (this.current == 0) {
					that.delIdlist = [];
					that.allchange = false;
				} else {
					that.delIdlist2 = [];
					that.allchange2 = false;
				}
			}
		},
		isNewAllChange() {
			let that = this;
			let delIdlist = that.current == 0 ? that.delIdlist : that.delIdlist2;
			let dynamicList = that.current == 0 ? that.dynamicList : that.dynamicList2;
			let allchange = true;
			if (delIdlist.length == 0) {
				if (that.current == 0) {
					that.allchange = false;
				} else {
					that.allchange2 = false;
				}
				return;
			}
			for (let i = 0; i < dynamicList.length; i++) {
				if (delIdlist.indexOf(dynamicList[i].id) == -1) {
					allchange = false;
				}
			}
			if (that.current == 0) {
				that.allchange = allchange;
			} else {
				that.allchange2 = allchange;
			}
		},
		openDelPopup() {
			this.$refs.popupCommonDelRef.open();
		},
		getCommonDel() {
			let that = this;
			let dynamic_id_str = '';
			if (that.delIdlist2.length > 0) {
				dynamic_id_str = that.delIdlist2.toString();
				dynamic_id_str = dynamic_id_str.split(',').join(',');
			}
			let data = {
				ids: dynamic_id_str
			};
			if (that.delLoading) return;
			that.delLoading = true;
			that.$refs.loadingCommonRef.open();
			that.$refs.popupCommonDelRef.close();
			dynamicBatchDel(data).then((res) => {
				setTimeout(() => {
					that.$refs.loadingCommonRef.close();
					that.delLoading = false;
					if (res.code == 0) {
						that.$refs.ttToastRef.text = '删除成功';
						that.$refs.ttToastRef.open();
						that.refreshAll();
					} else {
						that.$refs.ttToastRef.text = res.message;
						that.$refs.ttToastRef.open();
					}
				}, 300);
			});
		},

		openOnOffPopupRef() {
			let that = this;
			if (that.current == 0) {
				that.$refs.popupCommonOffRef.open();
			} else {
				that.$refs.popupCommonOnRef.open();
			}
		},

		// 批量上下架
		getDynamicBatchUpperDown() {
			let that = this;
			let dynamic_id_str = '';
			let delIdlist = [];
			if (that.current == 0) {
				delIdlist = that.delIdlist;
			} else {
				delIdlist = that.delIdlist2;
			}
			if (delIdlist.length > 0) {
				dynamic_id_str = delIdlist.toString();
				dynamic_id_str = dynamic_id_str.split(',').join(',');
			}
			let data = {
				status: that.current == 0 ? '0' : '1',
				ids: dynamic_id_str
			};
			if (that.delLoading) return;
			that.delLoading = true;
			that.$refs.loadingCommonRef.open();
			if (that.current == 0) {
				that.$refs.popupCommonOffRef.close();
			} else {
				that.$refs.popupCommonOnRef.close();
			}

			dynamicBatchOperate(data).then((res) => {
				setTimeout(() => {
					that.$refs.loadingCommonRef.close();
					that.delLoading = false;
					if (res.code == 0) {
						if (that.current == 0) {
							that.$refs.ttToastRef.text = '下架成功';
						} else {
							that.$refs.ttToastRef.text = '上架成功';
						}
						that.$refs.ttToastRef.open();
						that.refreshAll();
					} else {
						that.$refs.ttToastRef.text = res.message;
						that.$refs.ttToastRef.open();
					}
				}, 300);
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.out {
	height: 100vh;
	overflow-y: auto;
}
.footer_content_z {
	position: relative;
	width: 100%;
	height: 128rpx;
}

.topBg {
	background-color: #f3f4f6;
}

.navTop {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;

	&-left {
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;

		&-image {
			padding: 0 30rpx;
			height: 30rpx;
		}
	}

	&-center {
		flex: 1;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;

		&-title {
			padding-right: 60rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #000000;
		}
	}
}

::v-deep .u-tabs__wrapper__nav__line {
	bottom: 0 !important;
}

::v-deep .u-tabs__wrapper__nav__item {
	height: 88rpx !important;
	width: 300rpx;
}

.utabs {
	width: calc(100% - 2px);
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.searchLine {
	width: 100%;
	height: 98rpx;
	display: flex;
	align-items: center;
	justify-content: center;

	&-search {
		position: relative;
		width: 690rpx;
		height: 58rpx;
		display: flex;
		align-items: center;
		background-color: #eaeaea;
		border-radius: 50rpx;

		.search_album_iocn {
			width: 26rpx;
			height: 26rpx;
			margin-left: 30rpx;
			display: block;
		}

		.search_album {
			width: 26rpx;
			height: 26rpx;
			margin-right: 30rpx;
			display: block;
		}

		.search_album_input {
			flex: 1;
			height: 58rpx;
			padding: 0 20rpx;
			font-size: 24rpx;
		}
	}
}

.boxContent {
	position: relative;
	width: 100%;
	height: calc(100vh - 168rpx - 88rpx);
}

.notmorelist {
	position: relative;
	width: 100%;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #999999;
	font-size: 24rpx;
}

.not_content {
	position: relative;
	width: 100%;
	height: 50vh;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	font-size: 30rpx;
	font-weight: normal;
	color: #999999;

	&-image {
		width: 200rpx;
		height: 200rpx;
		display: block;
		margin-bottom: 30rpx;
	}
}

.tabs {
	display: flex;
	&-item {
		border-bottom: 1rpx solid rgba(#999999, 0.2);
		width: 50%;
		display: flex;
		align-items: center;
		flex-direction: column;
		&-c {
			width: 100%;
			align-items: center;
			height: 88rpx;
			justify-content: space-between;
			display: flex;

			&-text {
				flex: 1;
				text-align: center;
				font-size: 30rpx;
				color: #999999;
			}

			.checked {
				flex: 1;
				text-align: center;
				font-size: 30rpx;
				color: #6cbe70;
			}
		}

		&-underline {
			display: block;
			width: 40rpx;
			height: 6rpx;
		}

		// .checkedUnderline {
		// 	display: block;
		// 	width: 40rpx;
		// 	height: 6rpx;
		// 	background-color: #6cbe70;
		// }
	}
}

.dline {
	position: relative;
	width: 100%;
	padding: 30rpx 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid #f1f1f1;

	&-left {
		margin-left: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		&-change {
			width: 36rpx;
			height: 36rpx;
			display: block;
		}
	}

	&-right {
		width: calc(100% - 116rpx);
		margin-right: 30rpx;
		margin-left: 20rpx;
		flex: 1;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;

		&-top {
			width: 614rpx;
			padding: 10rpx;
			font-size: 26rpx;
			color: #333333;
			text-overflow: ellipsis;
			/*设置隐藏部分为省略号*/
			overflow: hidden;
			white-space: nowrap;
			display: inline-block; /* 这是关键 */
		}

		&-bottom {
			margin-top: 10rpx;
			width: 100%;
			height: 110rpx;
			display: grid;
			grid-template-columns: repeat(auto-fill, 110rpx);
			grid-gap: 0rpx 10rpx;

			.imageItem {
				position: relative;
				width: 110rpx;
				height: 110rpx;

				&-image {
					width: 110rpx;
					height: 110rpx;
					display: block;
				}

				&-mask {
					position: absolute;
					top: 0;
					left: 0;
					width: 110rpx;
					height: 110rpx;
					background-color: rgba(#000000, 0.4);
					font-size: 30rpx;
					color: #ffffff;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
	}
}

.footerBtnBox {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 690rpx;
	height: 100rpx;
	padding: 0 30rpx;
	background-color: #ffffff;
	z-index: 3090;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-top: 1px solid #f7f7f7;

	&-change {
		flex: 1;
		display: flex;
		align-items: center;

		&-image {
			width: 40rpx;
			height: 40rpx;
			display: block;
			margin-right: 20rpx;
		}

		&-c {
			display: flex;
			align-items: center;
			justify-content: center;
			&-img {
				width: 16rpx;
				height: 24rpx;
				display: block;
				margin-left: 20rpx;
			}

			&-text {
				font-size: 26rpx;
				color: #333333;
			}
		}
	}

	&-btn {
		width: 260rpx;
		height: 72rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 12rpx;
		background-color: #6cbe70;
		color: #ffffff;
		font-size: 26rpx;
	}

	&-notbtn {
		width: 260rpx;
		height: 72rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 12rpx;
		background-color: rgba(#6cbe70, 0.3);
		color: #ffffff;
		font-size: 26rpx;
	}

	&-btn2 {
		margin-left: 10rpx;
		width: 180rpx;
		height: 72rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 12rpx;
		background-color: #6cbe70;
		color: #ffffff;
		font-size: 26rpx;
	}

	&-notbtn2 {
		margin-left: 10rpx;
		width: 180rpx;
		height: 72rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 12rpx;
		background-color: rgba(#6cbe70, 0.3);
		color: #ffffff;
		font-size: 26rpx;
	}
}

.popup {
	position: relative;
	width: 100%;

	&-bottomBox {
		width: 220rpx;
		overflow: hidden;
		background-color: #ffffff;
		border-radius: 12rpx;
		margin: 0 30rpx;
		margin-bottom: 114rpx;

		&-line {
			text-align: center;
			padding: 0 30rpx;
			width: calc(100% - 60rpx);
			height: 76rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 26rpx;
			color: #333333;
		}

		&-divider {
			margin: 0 30rpx;
			border-bottom: 1rpx solid rgba(#999999, 0.2);
		}
	}
}
</style>
